using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;

public class DialogueViewer : EditorWindow
{
    ViewDialogueTree treeView; // 对话节点视图实例的公共属性
    ViewDialogueInspector inspectorView; // 对话节点视图实例的公共属性
    Label dialogueTreeIDLabel;

    [MenuItem("工具/DialogueViewer _#%E")] // #=shift, &=alt, %=ctrl
    public static void ShowExample()
    {
        DialogueViewer wnd = GetWindow<DialogueViewer>(); // 创建一个对话节点编辑器窗口实例
        wnd.titleContent = new GUIContent("DialogueViewer"); // 设置窗口标题

        // 获取当前选中的 DialogueTreeSO 实例
        DialogueTreeSO tree = Selection.activeObject as DialogueTreeSO;
        if (tree)
        {
            // 调用 OnSelectionChange 方法，立即显示当前选中的实例内容
            wnd.OnSelectionChange();
        }
    }

    [UnityEditor.Callbacks.OnOpenAsset(1)]
    public static bool OnOpenAsset(int instanceID, int line)
    {
        // 获取被双击的资产
        UnityEngine.Object obj = EditorUtility.InstanceIDToObject(instanceID);

        // 检查资产是否为 DialogueTreeSO 类型
        if (obj is DialogueTreeSO)
        {
            // 打开 DialogueViewer 窗口
            ShowExample();
            return true;
        }
        return false;
    }

    public void CreateGUI()
    {
        VisualElement root = rootVisualElement; // 获取根VisualElement

        var VisualTree = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>("Assets/Scripts/DialogueTree/Editor/DialogueViewer.uxml");
        VisualTree.CloneTree(root); // 克隆VisualTreeAsset中的元素到根VisualElement中

        var stylesheet = AssetDatabase.LoadAssetAtPath<StyleSheet>("Assets/Scripts/DialogueTree/Editor/DialogueViewer.uss");
        root.styleSheets.Add(stylesheet); // 将USS文件添加到根VisualElement的样式表中

        treeView = root.Q<ViewDialogueTree>(); // 获取对话节点视图实例
        inspectorView = root.Q<ViewDialogueInspector>(); // 获取对话节点视图实例

        dialogueTreeIDLabel = root.Q<Label>("DialogueTreeID"); // 获取 Label 组件

        // Button refreshViewButton = root.Q<Button>("RefreshView"); // 获取刷新视图按钮
        // refreshViewButton.clicked += () => EditorDialogueFeatures.RefreshView(treeView, inspectorView, dialogueTreeIDLabel);

        // Button verticalSortingButton = root.Q<Button>("VerticalSorting"); // 获取排列按钮
        // verticalSortingButton.clicked += () => EditorDialogueFeatures.VerticalSort(treeView);

        // Button MiniMapButton = root.Q<Button>("MiniMap"); // 获取地图按钮
        // MiniMapButton.clicked += () => treeView.ToggleMiniMap(); // 添加点击事件处理，切换MiniMap的显示/隐藏

        treeView.OnNodeSelected = OnNodeSelectionChaned; // 设置节点选择回调函数
    }

    private void OnNodeSelectionChaned(ViewDialogueNode view) // 当节点被选择时
    {
        inspectorView.UpdateSelection(view); // 填充节点视图
    }

    private void OnSelectionChange()
    {
        DialogueTreeSO tree = Selection.activeObject as DialogueTreeSO;
        if (tree)
        {
            treeView.PopulateView(tree);
            treeView.EnsureRootNodeExists(tree); // 确保根节点存在

            dialogueTreeIDLabel.text = tree.name; // 更新 Label 组件的文本
        }
    }
}